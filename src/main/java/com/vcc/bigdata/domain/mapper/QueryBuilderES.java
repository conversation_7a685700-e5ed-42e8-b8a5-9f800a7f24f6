package com.vcc.bigdata.domain.mapper;

import com.vcc.bigdata.domain.model.DateTimeFilter;
import com.vcc.bigdata.domain.model.DateTimeRangeFilter;
import com.vcc.bigdata.domain.model.audience.Audience;
import com.vcc.bigdata.domain.model.profile.ProfileManagement;
import com.vcc.bigdata.shared.Constants;
import com.vcc.bigdata.shared.utility.DateTimes;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.AbstractQueryBuilder;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;

import java.util.*;

@Slf4j
public class QueryBuilderES {
    /**
     * Builds the top-level Elasticsearch query for audience segments by combining multiple groups with AND/OR logic
     */
    public static BoolQueryBuilder getSegmentQuery(Audience.SegmentRule segmentRule,
                                                   Map<String, ProfileManagement.Mapping> mappingMap) throws Exception {
        if (segmentRule == null)
            return null;
        if (segmentRule.getGroups().size() == 1)
            return buildQueryPerGroup(segmentRule.getGroups().get(0), mappingMap);

        List<BoolQueryBuilder> groupsQueryBuilder = new ArrayList<>();
        for (Audience.Group group : segmentRule.getGroups())
            groupsQueryBuilder.add(buildQueryPerGroup(group, mappingMap));

        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        if (Objects.equals(segmentRule.getGroupExpression(), Constants.AND_EXPRESSION)) {
            groupsQueryBuilder.forEach(queryBuilder::must);
        } else if (Objects.equals(segmentRule.getGroupExpression(), Constants.OR_EXPRESSION)) {
            groupsQueryBuilder.forEach(queryBuilder::should);
            queryBuilder.minimumShouldMatch(1);
        } else
            throw new Exception("Expression not supported");

        return queryBuilder;
    }

    public static BoolQueryBuilder buildQueryPerGroup(Audience.Group group,
                                                      Map<String, ProfileManagement.Mapping> mappingMap) throws Exception {
        List<AbstractQueryBuilder> rulesQueryBuilder = new ArrayList<>();
        List<AbstractQueryBuilder> negativeRulesQueryBuilder = new ArrayList<>();

        for (Audience.Rule rule : group.getRules()) {
            buildSimpleDataTypeQuery(rule, mappingMap, rulesQueryBuilder, negativeRulesQueryBuilder);
        }

        BoolQueryBuilder groupQueryBuilder = new BoolQueryBuilder();
        if (rulesQueryBuilder.size() == 1 && negativeRulesQueryBuilder.isEmpty()) {
            groupQueryBuilder.must(rulesQueryBuilder.get(0));
            return groupQueryBuilder;
        } else if (rulesQueryBuilder.isEmpty()) {
            negativeRulesQueryBuilder.forEach(groupQueryBuilder::mustNot);
            return groupQueryBuilder;
        }

        if (Objects.equals(group.getRuleExpression(), Constants.AND_EXPRESSION)) {
            rulesQueryBuilder.forEach(groupQueryBuilder::must);
            negativeRulesQueryBuilder.forEach(groupQueryBuilder::mustNot);
        } else if (Objects.equals(group.getRuleExpression(), Constants.OR_EXPRESSION)) {
            rulesQueryBuilder.forEach(groupQueryBuilder::should);
            groupQueryBuilder.minimumShouldMatch(1);

            if (!negativeRulesQueryBuilder.isEmpty()) {
                BoolQueryBuilder negativeBuilder = new BoolQueryBuilder();
                negativeRulesQueryBuilder.forEach(negativeBuilder::mustNot);
                groupQueryBuilder.should(negativeBuilder);
            }
        } else
            throw new Exception("Expression not supported");

        return groupQueryBuilder;
    }

    private static List<ProfileManagement.Mapping> getInnerMapping(ProfileManagement.Mapping mappingParentField) {
        List<ProfileManagement.Mapping> mappings = new ArrayList<>();
        mappings.add(mappingParentField);
        if (mappingParentField.getSubFields() != null) {
            mappingParentField.getSubFields().values().forEach(mapping -> mappings.addAll(getInnerMapping(mapping)));
        }
        return mappings;
    }

    /**
     * Hàm lấy hashcode của field
     *
     * @param mappingMap
     * @return
     */
    public static String getFieldHashCode(Map<String, ProfileManagement.Mapping> mappingMap, String fieldName) {
        for (Map.Entry<String, ProfileManagement.Mapping> entry : mappingMap.entrySet()) {
            ProfileManagement.Mapping mapping = entry.getValue();

            // Kiểm tra nếu fieldMapping là "url"
            if (fieldName.equals(mapping.getFieldMapping())) {
                // Trả về hashcode của fieldName
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * Hàm build query cho mỗi rule
     *
     * @param rule                      thông tin rule (field name, data type,
     *                                  field_mapping, is_system_data, compare,
     *                                  value)
     * @param mappingMap
     * @param rulesQueryBuilder
     * @param negativeRulesQueryBuilder
     * @throws Exception
     */
    private static void buildSimpleDataTypeQuery(Audience.Rule rule,
                                                 Map<String, ProfileManagement.Mapping> mappingMap, List<AbstractQueryBuilder> rulesQueryBuilder,
                                                 List<AbstractQueryBuilder> negativeRulesQueryBuilder) throws Exception {
        String frequencyViewFieldMapping = getFieldHashCode(mappingMap, "frequency view");
        String frequencyViewCustomFieldMapping = getFieldHashCode(mappingMap, "frequency url");
        if (rule.getFieldName().equals(frequencyViewFieldMapping)) {
            // if (rule.getFieldName().equals("frequency_view")) {
            String urlFieldMapping = getFieldHashCode(mappingMap, "url");
            if (urlFieldMapping != null) {
                String pattern = "nganh-hoc";
                switch (rule.getCompare()) {
                    case "=":
                        // CÁCH 2: Đếm số url có chứa pattern
                        rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                                ScriptType.INLINE,
                                "painless",
                                "int count = 0; " +
                                        "for (u in doc['data." + urlFieldMapping + ".keyword']) { " +
                                        "    if (u.contains('" + pattern + "')) { count++; } " +
                                        "} " +
                                        "return count = " + rule.getValue() + ";",
                                Collections.emptyMap())));

                        // CÁCH 1: chỉ đếm số lượng url
                        // rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                        // ScriptType.INLINE,
                        // "painless",
                        // "doc['data." + urlFieldMapping + ".keyword'].length == " + rule.getValue(),
                        // Collections.emptyMap()
                        // )));
                        break;
                    case ">=":
                        // CÁCH 2: Đếm số url có chứa pattern
                        rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                                ScriptType.INLINE,
                                "painless",
                                "int count = 0; " +
                                        "for (u in doc['data." + urlFieldMapping + ".keyword']) { " +
                                        "    if (u.contains('" + pattern + "')) { count++; } " +
                                        "} " +
                                        "return count >= " + rule.getValue() + ";",
                                Collections.emptyMap())));

                        // CÁCH 1: chỉ đếm số lượng url
                        // rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                        // ScriptType.INLINE,
                        // "painless",
                        // "doc['data." + urlFieldMapping + ".keyword'].length >= " + rule.getValue(),
                        // Collections.emptyMap()
                        // )));
                        break;
                    case ">":
                        // CÁCH 2: Đếm số url có chứa pattern
                        rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                                ScriptType.INLINE,
                                "painless",
                                "int count = 0; " +
                                        "for (u in doc['data." + urlFieldMapping + ".keyword']) { " +
                                        "    if (u.contains('" + pattern + "')) { count++; } " +
                                        "} " +
                                        "return count > " + rule.getValue() + ";",
                                Collections.emptyMap())));

                        // CÁCH 1: chỉ đếm số lượng url
                        // rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                        // ScriptType.INLINE,
                        // "painless",
                        // "doc['data." + urlFieldMapping + ".keyword'].length > " + rule.getValue(),
                        // Collections.emptyMap()
                        // )));
                        break;
                    case "<=":
                        // CÁCH 2: Đếm số url có chứa pattern
                        rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                                ScriptType.INLINE,
                                "painless",
                                "int count = 0; " +
                                        "for (u in doc['data." + urlFieldMapping + ".keyword']) { " +
                                        "    if (u.contains('" + pattern + "')) { count++; } " +
                                        "} " +
                                        "return count <= " + rule.getValue() + ";",
                                Collections.emptyMap())));

                        // CÁCH 1: chỉ đếm số lượng url
                        // rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                        // ScriptType.INLINE,
                        // "painless",
                        // "doc['data." + urlFieldMapping + ".keyword'].length <= " + rule.getValue(),
                        // Collections.emptyMap()
                        // )));
                        break;
                    case "<":
                        // CÁCH 2: Đếm số url có chứa pattern
                        rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                                ScriptType.INLINE,
                                "painless",
                                "int count = 0; " +
                                        "for (u in doc['data." + urlFieldMapping + ".keyword']) { " +
                                        "    if (u.contains('" + pattern + "')) { count++; } " +
                                        "} " +
                                        "return count < " + rule.getValue() + ";",
                                Collections.emptyMap())));

                        // CÁCH 1: chỉ đếm số lượng url
                        // rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                        // ScriptType.INLINE,
                        // "painless",
                        // "doc['data." + urlFieldMapping + ".keyword'].length < " + rule.getValue(),
                        // Collections.emptyMap()
                        // )));
                        break;

                }
            }
        }
        else if (rule.getFieldName().equals(frequencyViewCustomFieldMapping) && rule.getPattern() != null && !rule.getPattern().isEmpty()) {
            // xử lý field frequency url
            String pattern = rule.getPattern();
            String urlFieldMapping = getFieldHashCode(mappingMap, "url");
            switch (rule.getCompare()) {
                case "=":
                    // CÁCH 2: Đếm số url có chứa pattern
                    rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                            ScriptType.INLINE,
                            "painless",
                            "int count = 0; " +
                                    "for (u in doc['data." + urlFieldMapping + ".keyword']) { " +
                                    "    if (u.contains('" + pattern + "')) { count++; } " +
                                    "} " +
                                    "return count = " + rule.getValue() + ";",
                            Collections.emptyMap())));
                    break;
                case ">=":
                    // CÁCH 2: Đếm số url có chứa pattern
                    rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                            ScriptType.INLINE,
                            "painless",
                            "int count = 0; " +
                                    "for (u in doc['data." + urlFieldMapping + ".keyword']) { " +
                                    "    if (u.contains('" + pattern + "')) { count++; } " +
                                    "} " +
                                    "return count >= " + rule.getValue() + ";",
                            Collections.emptyMap())));
                    break;
                case ">":
                    // CÁCH 2: Đếm số url có chứa pattern
                    rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                            ScriptType.INLINE,
                            "painless",
                            "int count = 0; " +
                                    "for (u in doc['data." + urlFieldMapping + ".keyword']) { " +
                                    "    if (u.contains('" + pattern + "')) { count++; } " +
                                    "} " +
                                    "return count > " + rule.getValue() + ";",
                            Collections.emptyMap())));
                    break;
                case "<=":
                    // CÁCH 2: Đếm số url có chứa pattern
                    rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                            ScriptType.INLINE,
                            "painless",
                            "int count = 0; " +
                                    "for (u in doc['data." + urlFieldMapping + ".keyword']) { " +
                                    "    if (u.contains('" + pattern + "')) { count++; } " +
                                    "} " +
                                    "return count <= " + rule.getValue() + ";",
                            Collections.emptyMap())));
                    break;
                case "<":
                    // CÁCH 2: Đếm số url có chứa pattern
                    rulesQueryBuilder.add(QueryBuilders.scriptQuery(new Script(
                            ScriptType.INLINE,
                            "painless",
                            "int count = 0; " +
                                    "for (u in doc['data." + urlFieldMapping + ".keyword']) { " +
                                    "    if (u.contains('" + pattern + "')) { count++; } " +
                                    "} " +
                                    "return count < " + rule.getValue() + ";",
                            Collections.emptyMap())));
                    break;

            }
        } else {
            String dataType = mappingMap.get(rule.getFieldName()).getDataType();
            switch (dataType) {
                case Constants.DATA_TYPE_DATETIME:
                case Constants.DATA_TYPE_DATE:
                    switch (rule.getCompare()) {
                        case "equal":
                            DateTimeFilter dateTimeFilter = DateTimes.parseSingleDateTime(rule.getValue().toString());
                            rulesQueryBuilder.add(QueryBuilders.scriptQuery(buildScriptCompareDateTime(
                                    "data." + rule.getFieldName() + "_t", dateTimeFilter, "==")));
                            break;
                        case "before":
                            DateTimeFilter dateTimeFilterBefore = DateTimes
                                    .parseSingleDateTime(rule.getValue().toString());
                            rulesQueryBuilder.add(QueryBuilders.scriptQuery(buildScriptCompareDateTime(
                                    "data." + rule.getFieldName() + "_t", dateTimeFilterBefore, "<")));
                            break;
                        case "after":
                            DateTimeFilter dateTimeFilterAfter = DateTimes
                                    .parseSingleDateTime(rule.getValue().toString());
                            rulesQueryBuilder.add(QueryBuilders.scriptQuery(buildScriptCompareDateTime(
                                    "data." + rule.getFieldName() + "_t", dateTimeFilterAfter, ">")));
                            break;
                        case "before_or_equal":
                            DateTimeFilter dateTimeFilterBeforeOrEqual = DateTimes
                                    .parseSingleDateTime(rule.getValue().toString());
                            rulesQueryBuilder.add(QueryBuilders.scriptQuery(buildScriptCompareDateTime(
                                    "data." + rule.getFieldName() + "_t", dateTimeFilterBeforeOrEqual, "<=")));
                            break;
                        case "after_or_equal":
                            DateTimeFilter dateTimeFilterAfterOrEqual = DateTimes
                                    .parseSingleDateTime(rule.getValue().toString());
                            rulesQueryBuilder.add(QueryBuilders.scriptQuery(buildScriptCompareDateTime(
                                    "data." + rule.getFieldName() + "_t", dateTimeFilterAfterOrEqual, ">=")));
                            break;
                        case "between":
                            DateTimeRangeFilter dateTimeRangeFilter = DateTimes
                                    .parseDateTimeRange(rule.getValue().toString());
                            DateTimeFilter dateTimeFilterBetweenStart = dateTimeRangeFilter.getStart();
                            DateTimeFilter dateTimeFilterBetweenEnd = dateTimeRangeFilter.getEnd();
                            rulesQueryBuilder.add(QueryBuilders.scriptQuery(buildScriptCompareDateTime(
                                    "data." + rule.getFieldName() + "_t", dateTimeFilterBetweenStart, ">=")));
                            rulesQueryBuilder.add(QueryBuilders.scriptQuery(buildScriptCompareDateTime(
                                    "data." + rule.getFieldName() + "_t", dateTimeFilterBetweenEnd, "<=")));
                            break;
                        case "is_null":
                            negativeRulesQueryBuilder.add(QueryBuilders.existsQuery("data." + rule.getFieldName()));
                            break;
                        case "is_not_null":
                            rulesQueryBuilder.add(QueryBuilders.existsQuery("data." + rule.getFieldName()));
                            break;
                        default:
                            throw new Exception("Compare not understood");
                    }
                    break;
                case Constants.DATA_TYPE_TEXT:
                    switch (rule.getCompare()) {
                        case "contain":
                            rulesQueryBuilder.add(QueryBuilders
                                    .wildcardQuery("data." + rule.getFieldName() + ".keyword",
                                            "*" + rule.getValue() + "*")
                                    .caseInsensitive(true));
                            break;
                        case "startwith":
                            rulesQueryBuilder
                                    .add(QueryBuilders.wildcardQuery("data." + rule.getFieldName() + ".keyword",
                                            rule.getValue() + "*"));
                            break;
                        case "endwith":
                            rulesQueryBuilder
                                    .add(QueryBuilders.wildcardQuery("data." + rule.getFieldName() + ".keyword",
                                            "*" + rule.getValue()));
                            break;
                        case "not_startwith":
                            negativeRulesQueryBuilder.add(QueryBuilders
                                    .wildcardQuery("data." + rule.getFieldName() + ".keyword", rule.getValue() + "*"));
                            break;
                        case "not_endwith":
                            negativeRulesQueryBuilder.add(QueryBuilders
                                    .wildcardQuery("data." + rule.getFieldName() + ".keyword", "*" + rule.getValue()));
                            break;
                        case "not_contain":
                            negativeRulesQueryBuilder.add(QueryBuilders
                                    .wildcardQuery("data." + rule.getFieldName() + ".keyword",
                                            "*" + rule.getValue() + "*")
                                    .caseInsensitive(true));
                            break;
                        case "is_null":
                            negativeRulesQueryBuilder.add(QueryBuilders.existsQuery("data." + rule.getFieldName()));
                            break;
                        case "is_not_null":
                            rulesQueryBuilder.add(QueryBuilders.existsQuery("data." + rule.getFieldName()));
                            break;
                        default:
                            throw new Exception("Compare not understood");
                    }
                    break;
                case Constants.DATA_TYPE_NUMBER:
                    switch (rule.getCompare()) {
                        case "=":
                            rulesQueryBuilder
                                    .add(QueryBuilders.termQuery("data." + rule.getFieldName(), rule.getValue()));
                            break;
                        case ">=":
                            rulesQueryBuilder
                                    .add(QueryBuilders.rangeQuery("data." + rule.getFieldName()).gte(rule.getValue()));
                            break;
                        case ">":
                            rulesQueryBuilder
                                    .add(QueryBuilders.rangeQuery("data." + rule.getFieldName()).gt(rule.getValue()));
                            break;
                        case "<=":
                            rulesQueryBuilder
                                    .add(QueryBuilders.rangeQuery("data." + rule.getFieldName()).lte(rule.getValue()));
                            break;
                        case "<":
                            rulesQueryBuilder
                                    .add(QueryBuilders.rangeQuery("data." + rule.getFieldName()).lt(rule.getValue()));
                            break;
                        case "!=":
                            negativeRulesQueryBuilder
                                    .add(QueryBuilders.termQuery("data." + rule.getFieldName(), rule.getValue()));
                            break;
                        case "is_null":
                            negativeRulesQueryBuilder.add(QueryBuilders.existsQuery("data." + rule.getFieldName()));
                            break;
                        case "is_not_null":
                            rulesQueryBuilder.add(QueryBuilders.existsQuery("data." + rule.getFieldName()));
                            break;
                        default:
                            throw new Exception("Compare not understood");
                    }
                    break;
                case Constants.DATA_TYPE_ARRAY:
                    switch (rule.getCompare()) {
                        case "is_null":
                            negativeRulesQueryBuilder.add(QueryBuilders.existsQuery("data." + rule.getFieldName()));
                            break;
                        case "is_not_null":
                            rulesQueryBuilder.add(QueryBuilders.existsQuery("data." + rule.getFieldName()));
                            break;
                        default:
                            throw new Exception("Compare not understood");
                    }
                    break;
                case Constants.DATA_TYPE_OBJECT:
                    switch (rule.getCompare()) {
                        case "is_null":
                            negativeRulesQueryBuilder.add(QueryBuilders.existsQuery("data." + rule.getFieldName()));
                            break;
                        case "is_not_null":
                            rulesQueryBuilder.add(QueryBuilders.existsQuery("data." + rule.getFieldName()));
                            break;
                        default:
                            throw new Exception("Compare not understood");
                    }
                    break;
                default:
                    throw new Exception("Datatype not supported");
            }
        }
    }

    private static Script buildScriptCompareDateTime(String fieldName, DateTimeFilter dateTimeFilter, String operator) {
        StringBuilder scriptBuilder = new StringBuilder();
        scriptBuilder.append("if (doc['").append(fieldName).append("'].size() == 0) { return false; } ");
        scriptBuilder.append("for (date in doc['").append(fieldName).append("']) { ");

        if (dateTimeFilter.hasOnlyOneFieldAvailable() || operator.equals("==")) {
            scriptBuilder.append("    boolean match = true; ");
            if (dateTimeFilter.getYear() != null) {
                scriptBuilder.append("    match = match && date.getYear() ").append(operator).append(" ")
                        .append(dateTimeFilter.getYear()).append("; ");
            }
            if (dateTimeFilter.getMonth() != null) {
                scriptBuilder.append("    match = match && date.getMonthValue() ").append(operator).append(" ")
                        .append(dateTimeFilter.getMonth()).append("; ");
            }
            if (dateTimeFilter.getDay() != null) {
                scriptBuilder.append("    match = match && date.getDayOfMonth() ").append(operator).append(" ")
                        .append(dateTimeFilter.getDay()).append("; ");
            }
            if (dateTimeFilter.getHour() != null) {
                scriptBuilder.append("    match = match && date.getHour() ").append(operator).append(" ")
                        .append(dateTimeFilter.getHour()).append("; ");
            }
            if (dateTimeFilter.getMinute() != null) {
                scriptBuilder.append("    match = match && date.getMinute() ").append(operator).append(" ")
                        .append(dateTimeFilter.getMinute()).append("; ");
            }
            if (dateTimeFilter.getSecond() != null) {
                scriptBuilder.append("    match = match && date.getSecond() ").append(operator).append(" ")
                        .append(dateTimeFilter.getSecond()).append("; ");
            }
            scriptBuilder.append("    if (match) { return true; } ");
        } else {
            if (dateTimeFilter.isContinuous()) {
                String defaultYear = String.valueOf(dateTimeFilter.getYear());
                String defaultMonth = String.valueOf(dateTimeFilter.getMonth());
                String defaultDay = "1";
                String defaultHour = "0";
                String defaultMinute = "0";
                String defaultSecond = "0";

                if (operator.equals(">") || operator.equals("<=")) {
                    defaultDay = String.valueOf(getDaysInMonth(dateTimeFilter.getYear(), dateTimeFilter.getMonth()));
                    defaultHour = "23";
                    defaultMinute = "59";
                    defaultSecond = "59";
                }

                scriptBuilder.append("    long dateMillis = date.toInstant().toEpochMilli();");
                scriptBuilder.append("    long filterMillis = ZonedDateTime.of(")
                        .append(dateTimeFilter.getYear() != null ? dateTimeFilter.getYear() : defaultYear).append(", ")
                        .append(dateTimeFilter.getMonth() != null ? dateTimeFilter.getMonth() : defaultMonth)
                        .append(", ")
                        .append(dateTimeFilter.getDay() != null ? dateTimeFilter.getDay() : defaultDay).append(", ")
                        .append(dateTimeFilter.getHour() != null ? dateTimeFilter.getHour() : defaultHour).append(", ")
                        .append(dateTimeFilter.getMinute() != null ? dateTimeFilter.getMinute() : defaultMinute)
                        .append(", ")
                        .append(dateTimeFilter.getSecond() != null ? dateTimeFilter.getSecond() : defaultSecond)
                        .append(", ")
                        .append("0, ZoneId.of(\"UTC\")).toInstant().toEpochMilli(); ");

                scriptBuilder.append("    if (dateMillis ").append(operator).append(" filterMillis) { return true; } ");
            } else {
                scriptBuilder.append("    boolean match = true; ");
                if (dateTimeFilter.getYear() != null) {
                    if (dateTimeFilter.isLastIndexAvailableField(0)) {
                        scriptBuilder.append("    match = match && date.getYear() ").append(operator).append(" ")
                                .append(dateTimeFilter.getYear()).append("; ");
                    } else {
                        if (operator.equals(">") || operator.equals(">=")) {
                            scriptBuilder.append("    match = match && date.getYear() >= ")
                                    .append(dateTimeFilter.getYear()).append("; ");
                        } else {
                            scriptBuilder.append("    match = match && date.getYear() <= ")
                                    .append(dateTimeFilter.getYear()).append("; ");
                        }
                    }
                }
                if (dateTimeFilter.getMonth() != null) {
                    if (dateTimeFilter.isLastIndexAvailableField(1)) {
                        scriptBuilder.append("    match = match && date.getMonthValue() ").append(operator).append(" ")
                                .append(dateTimeFilter.getMonth()).append("; ");
                    } else {
                        if (operator.equals(">") || operator.equals(">=")) {
                            scriptBuilder.append("    match = match && date.getMonthValue() >= ")
                                    .append(dateTimeFilter.getMonth()).append("; ");
                        } else {
                            scriptBuilder.append("    match = match && date.getMonthValue() <= ")
                                    .append(dateTimeFilter.getMonth()).append("; ");
                        }
                    }
                }
                if (dateTimeFilter.getDay() != null) {
                    if (dateTimeFilter.isLastIndexAvailableField(2)) {
                        scriptBuilder.append("    match = match && date.getDayOfMonth() ").append(operator).append(" ")
                                .append(dateTimeFilter.getDay()).append("; ");
                    } else {
                        if (operator.equals(">") || operator.equals(">=")) {
                            scriptBuilder.append("    match = match && date.getDayOfMonth() >= ")
                                    .append(dateTimeFilter.getDay()).append("; ");
                        } else {
                            scriptBuilder.append("    match = match && date.getDayOfMonth() <= ")
                                    .append(dateTimeFilter.getDay()).append("; ");
                        }
                    }
                }
                if (dateTimeFilter.getHour() != null) {
                    if (dateTimeFilter.isLastIndexAvailableField(3)) {
                        scriptBuilder.append("    match = match && date.getHour() ").append(operator).append(" ")
                                .append(dateTimeFilter.getHour()).append("; ");
                    } else {
                        if (operator.equals(">") || operator.equals(">=")) {
                            scriptBuilder.append("    match = match && date.getHour() >= ")
                                    .append(dateTimeFilter.getHour()).append("; ");
                        } else {
                            scriptBuilder.append("    match = match && date.getHour() <= ")
                                    .append(dateTimeFilter.getHour()).append("; ");
                        }
                    }
                }
                if (dateTimeFilter.getMinute() != null) {
                    if (dateTimeFilter.isLastIndexAvailableField(4)) {
                        scriptBuilder.append("    match = match && date.getMinute() ").append(operator).append(" ")
                                .append(dateTimeFilter.getMinute()).append("; ");
                    } else {
                        if (operator.equals(">") || operator.equals(">=")) {
                            scriptBuilder.append("    match = match && date.getMinute() >= ")
                                    .append(dateTimeFilter.getMinute()).append("; ");
                        } else {
                            scriptBuilder.append("    match = match && date.getMinute() <= ")
                                    .append(dateTimeFilter.getMinute()).append("; ");
                        }
                    }
                }
                if (dateTimeFilter.getSecond() != null) {
                    if (dateTimeFilter.isLastIndexAvailableField(5)) {
                        scriptBuilder.append("    match = match && date.getSecond() ").append(operator).append(" ")
                                .append(dateTimeFilter.getSecond()).append("; ");
                    } else {
                        if (operator.equals(">") || operator.equals(">=")) {
                            scriptBuilder.append("    match = match && date.getSecond() >= ")
                                    .append(dateTimeFilter.getSecond()).append("; ");
                        } else {
                            scriptBuilder.append("    match = match && date.getSecond() <= ")
                                    .append(dateTimeFilter.getSecond()).append("; ");
                        }
                    }
                }
                scriptBuilder.append("    if (match) { return true; } ");
            }
        }

        scriptBuilder.append("} ");
        scriptBuilder.append("return false;");

        return new Script(ScriptType.INLINE, "painless", scriptBuilder.toString(), Collections.emptyMap());
    }

    private static boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }

    private static int getDaysInMonth(Integer year, Integer month) {
        switch (month) {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                return 31;
            case 4:
            case 6:
            case 9:
            case 11:
                return 30;
            case 2:
                if (year != null) {
                    if (isLeapYear(year)) {
                        return 29;
                    } else {
                        return 28;
                    }
                }
                return 29;
            default:
                throw new IllegalArgumentException("Invalid month: " + month);
        }
    }
}
