package com.vcc.bigdata.presentation.controller.detail;

import com.vcc.bigdata.application.dto.activity.EventDto;
import com.vcc.bigdata.application.dto.activity.InteractionDto;
import com.vcc.bigdata.application.dto.log.ProfileLogDto;
import com.vcc.bigdata.application.dto.profile.*;
import com.vcc.bigdata.domain.service.profile.ProfileInfoService;
import com.vcc.bigdata.presentation.response.Response;
import com.vcc.bigdata.presentation.response.ResponseFactory;
import com.vcc.bigdata.shared.Constants;
import com.vcc.bigdata.shared.utility.Hashings;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

@RestController
@RequestMapping("/v1/profiles/")
public class ProfileInfoController {
    private final ProfileInfoService profileInfoService;

    public ProfileInfoController(ProfileInfoService profileInfoService) {
        this.profileInfoService = profileInfoService;
    }

    /**
     * Retrieves the profile information of a customer.
     * @param id the ID of the customer whose profile is to be retrieved
     * @return the profile information of the customer
     */
    @GetMapping("{id}")
    public Response getProfileInfo(@PathVariable String id,
                                                   @RequestHeader("user-id") String userId) {
        return ResponseFactory.getSuccessResponse("Success", profileInfoService.getProfileInfo(Hashings.CRC32(userId), id));
    }
    /**
     * API delete profile for customer
     * @param id customer id
     * @return response after deleting profile
     */
    @DeleteMapping("{id}")
    public Response deleteProfile(@PathVariable String id,
                                  @RequestHeader("user-id") String userId) {
        return ResponseFactory.getSuccessResponse("Success", null);
    }

    /**
     * API get profile score for customer
     * @param id customer id
     * @return response with profile score
     */
    @GetMapping("{id}/scores")
    public Response<ProfileScoreDto> getProfileScore(@PathVariable String id,
                                                     @RequestHeader("user-id") String userId) {
        ProfileScoreDto dto = ProfileScoreDto.builder()
                .profileId(String.valueOf(id))
                .leadScore(6.12)
                .clvScore(1L)
                .dataQualityScore(0.3)
                .engagementScore(0.2)
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();
        return ResponseFactory.getSuccessResponse("Success", dto);
    }

    /**
     * API get predictive score for customer
     * @param id customer id
     * @return response with predictive score
     */
    @GetMapping("{id}/predictive-scores")
    public Response<PredictionScoreDto> getPredictiveScore(@PathVariable String id,
                                                           @RequestHeader("user-id") String userId) {
        PredictionScoreDto predictionScoreDto = PredictionScoreDto.builder()
                .profileId(String.valueOf(id))
                .highLtv(6.12)
                .recommends(1.2)
                .purchaseFrequently(1.0)
                .convenienceShopper(1.0)
                .churn(1.0)
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();
        return ResponseFactory.getSuccessResponse("Success", predictionScoreDto);
    }


    /**
     * API get audience tags for customer
     * @param id customer id
     * @return response with audience tags
     */
    @GetMapping("{id}/audience-tags")
    public Response<List<AudienceTagDto>> getAudienceTags(@PathVariable String id,
                                                          @RequestHeader("user-id") String userId) {
        List<AudienceTagDto> audienceTags = new ArrayList<>();
        AudienceTagDto audienceTagDto = AudienceTagDto.builder()
                .profileId(String.valueOf(id))
                .audienceId("1")
                .audienceStudio("tag1")
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();
        audienceTags.add(audienceTagDto);

        return ResponseFactory.getSuccessResponse("Success", audienceTags);
    }

    /**
     * API get sources for customer
     * @param id customer id
     * @return response with sources
     */
    @GetMapping("{id}/sources")
    public Response<List<CustomerSourceDto>> getSources(@PathVariable String id,
                                                        @RequestHeader("user-id") String userId) {
        List<CustomerSourceDto> customerSourceDtos = new ArrayList<>();
        CustomerSourceDto customerSourceDto = CustomerSourceDto.builder()
                .customerId(String.valueOf(id))
                .sourceId("1")
                .sourceName("source1")
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();
        customerSourceDtos.add(customerSourceDto);
        return ResponseFactory.getSuccessResponse("Success", customerSourceDtos);
    }

    /**
     * API to get purchase details of a customer.
     * @param id customer id
     * @return response with purchase details
     */
    @GetMapping("{id}/purchases")
    public Response getPurchases(@PathVariable String id,
                                 @RequestHeader("user-id") String userId) {
        return null;
    }
    /**
     * Retrieves the purchase statistics of a customer.
     *
     * @param id the ID of the customer whose purchase statistics are to be retrieved
     * @return response containing the customer's purchase statistics
     */
    @GetMapping("{id}/purchases/statistics")
    public Response<CustomerPurchaseStatisticDto> getPurchasesStatistics(@PathVariable String id,
                                                                         @RequestHeader("user-id") String userId) {
        return ResponseFactory.getSuccessResponse("Success", new CustomerPurchaseStatisticDto(id,56, 2_459_260.46, 135_593_186.48));
    }
    /**
     * API to get connections of a customer.
     * @param id customer id
     * @return response with connections
     */
    @GetMapping("{id}/connections")
    public Response<ConnectionDto> getConnections(@PathVariable String id,
                                                  @RequestHeader("user-id") String userId) {
        Map<ConnectionDto.ChannelConnectType, List<String>> channels = new HashMap<>();
        List<String> emailList = new ArrayList<>();
        emailList.add("<EMAIL>");
        emailList.add("<EMAIL>");
        channels.put(ConnectionDto.ChannelConnectType.EMAIL, emailList);

        List<String> phoneList = new ArrayList<>();
        phoneList.add("0123456789");
        channels.put(ConnectionDto.ChannelConnectType.PHONE, phoneList);
        ConnectionDto connectionDto = ConnectionDto.builder()
                .profileId(id)
                .channels(channels)
                .build();

        return ResponseFactory.getSuccessResponse("Success", connectionDto);
    }

    /**
     * Retrieves the channel frequencies of a customer.
     * @param id the ID of the customer whose channel frequencies are to be retrieved
     * @return response containing the customer's channel frequencies
     */
    @GetMapping("{id}/channels")
    public Response<ChannelFreqDto> getChannels(@PathVariable String id,
                                                @RequestHeader("user-id") String userId) {
        List<ChannelFreqDto.ChannelFreqInfo> channels = new ArrayList<>();
        channels.add(ChannelFreqDto.ChannelFreqInfo.builder().channel(ChannelFreqDto.ChannelConnectType.EMAIL).freq(1).build());
        channels.add(ChannelFreqDto.ChannelFreqInfo.builder().channel(ChannelFreqDto.ChannelConnectType.WEBSITE).freq(30).build());
        channels.add(ChannelFreqDto.ChannelFreqInfo.builder().channel(ChannelFreqDto.ChannelConnectType.MOBILE).freq(12).build());
        ChannelFreqDto channelFreqDto = ChannelFreqDto.builder()
                .profileId(id)
                .channelFreqInfos(channels)
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();
        return ResponseFactory.getSuccessResponse("Success", channelFreqDto);
    }

    /**
     * API to retrieve email metrics for a customer.
     * @param id the ID of the customer whose email metrics are to be retrieved
     * @return response containing the customer's email metrics
     */
    @GetMapping("{id}/email-metrics")
    public Response<EmailMetricsDto> getEmailMetrics(@PathVariable String id,
                                                     @RequestHeader("user-id") String userId) {
        EmailMetricsDto emailMetricsDto = EmailMetricsDto.builder()
                .profileId(id)
                .delivered(2)
                .openRate(0.5)
                .clickRate(0.6)
                .conversionRate(0.7)
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();

        return ResponseFactory.getSuccessResponse("Success", emailMetricsDto);
    }
    /**
     * API get devices for customer
     * @param id customer id
     * @return response with devices
     */
    @GetMapping("{id}/devices")
    public Response<DeviceUsageDto> getDevices(@PathVariable String id,
                                               @RequestHeader("user-id") String userId) {
        List<DeviceUsageDto.DeviceUsageInfo> deviceUsageInfos = new ArrayList<>();

        deviceUsageInfos.add(DeviceUsageDto.DeviceUsageInfo.builder()
                .deviceType(DeviceUsageDto.DeviceType.MOBILE)
                .lastActiveAt(new Timestamp(System.currentTimeMillis())).build());
        deviceUsageInfos.add(DeviceUsageDto.DeviceUsageInfo.builder()
                .deviceType(DeviceUsageDto.DeviceType.DESKTOP)
                .lastActiveAt(new Timestamp(System.currentTimeMillis())).build());

        DeviceUsageDto deviceUsageDto = DeviceUsageDto.builder()
                .profileId(id)
                .deviceUsages(deviceUsageInfos)
                .lastUpdatedAt(new Timestamp(System.currentTimeMillis())).build();
        return ResponseFactory.getSuccessResponse("Success", deviceUsageDto);
    }

    /**
     * API get search history of customer
     * @param id customer id
     * @return response with search history
     */
    @GetMapping("{id}/search-history")
    public Response<SearchHistoryDto> getSearchHistory(@PathVariable String id,
                                                       @RequestHeader("user-id") String userId) {
        List<KeywordFreq> keywordFreqList = Arrays.asList(
                new KeywordFreq("cdp bizfly", 5),
                new KeywordFreq("shopping", 3)
        );

        SearchHistoryDto dto = SearchHistoryDto.builder()
                .profileId(id)
                .keywordFreq(keywordFreqList)
                .build();
        return ResponseFactory.getSuccessResponse("Success", dto);
    }

    /**
     * API get preferences of customer
     * @param id customer id
     * @return response with preferences
     */
    @GetMapping("{id}/preferences")
    public Response<PreferenceDto> getPreferences(@PathVariable String id,
                                                  @RequestHeader("user-id") String userId) {
        Map<String, Object> preferences = Map.of(
                "gender", Constants.GENDER_MALE,
                "age", 20
        );
        return ResponseFactory.getSuccessResponse("Success", PreferenceDto.builder()
                .profileId(id)
                .preferences(preferences));
    }

    /**
     * Trả về danh sách hoạt động (mock) của hồ sơ người dùng, gồm các tương tác như email, SMS, Facebook.
     *
     * @param id     ID của hồ sơ người dùng.
     * @param userId ID người gửi request (từ header "user-id").
     * @return Response chứa danh sách {@link InteractionDto} mô phỏng các tương tác gần đây.
     */
    @GetMapping("{id}/activities")
    public Response<EventDto> getActivities(@PathVariable String id,
                                            @RequestHeader("user-id") String userId) {
        List<InteractionDto> mockInteractions = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("campaign", "Chiến dịch " + i);
            mockInteractions.add(InteractionDto.builder()
                    .id(String.valueOf(i))
                    .timestamp(Instant.now().minus(i, ChronoUnit.DAYS))
                    .interactionType(i % 3 == 0 ? "email" : i % 3 == 1 ? "sms" : "facebook")
                    .content(i % 2 == 0 ? "Nội dung tương tác số " + i : null)
                    .link(i % 2 != 0 ? "https://example.com/link-" + i : null)
                    .extraMetadata(metadata)
                    .build());
        }
        return ResponseFactory.getSuccessResponse("Mock api", mockInteractions);
    }

    /**
     * Trả về danh sách log hoạt động (mock) của hồ sơ người dùng.
     *
     * @param id     ID của hồ sơ người dùng.
     * @param userId ID của người dùng gửi request (từ header "user-id").
     * @return Response chứa danh sách {@link ProfileLogDto} mô phỏng các log như tạo, cập nhật, xóa.
     */
    @GetMapping("{id}/logs-info")
    public Response<ProfileLogDto> getLogs(@PathVariable String id,
                                           @RequestHeader("user-id") String userId) {
        List<ProfileLogDto> mockLogs = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            mockLogs.add(ProfileLogDto.builder()
                    .id("log_" + i)
                    .profileId("cus_" + (i % 5))  // giả lập 5 khách hàng
                    .actionType(i % 3 == 0 ? "update" : i % 3 == 1 ? "create" : "delete")
                    .content("Thực hiện hành động " + i)
                    .updatedBy("user" + (i % 4) + "@example.com")
                    .timestamp(Instant.now().minus(i, ChronoUnit.HOURS))
                    .ipAddress("10.0.0." + (100 + i))
                    .build());
        }
        return ResponseFactory.getSuccessResponse("Mock api", mockLogs);
    }

}
